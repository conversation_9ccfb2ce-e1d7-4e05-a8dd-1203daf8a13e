// 云对象教程：https://uniapp.dcloud.net.cn/uniCloud/cloud-obj
const { log } = require('console')
const tencentcloud = require('tencentcloud-sdk-nodejs-asr')
const AsrClient = tencentcloud.asr.v20190614.Client
const clientConfig = {
  credential: {
    secretId: 'AKIDgFc3Pm9PXn1756rW0JRLEvPgkdIkz2tX',
    secretKey: 'XuOo7FBxW4RbdVBdswscGl7HEyVqysbB',
  },
  region: '',
  profile: {
    httpProfile: {
      endpoint: 'asr.tencentcloudapi.com',
    },
  },
}

module.exports = {
  _before: function () {},
  async asr() {
    const httpInfo = this.getHttpInfo()
	console.log('httpInfo.body', httpInfo.body)
    let { url } = JSON.parse(httpInfo.body)

    const client = new AsrClient(clientConfig)
    const params = {
      EngineModelType: '16k_zh',
      ChannelNum: 1,
      ResTextFormat: 2,
      SourceType: 0,
      Url: url,
      // CallbackUrl: 'https://fc-mp-c6815a6a-de20-45ad-a345-2e804f127311.next.bspapp.com/speak/asrCallback',
    }

    try {
      const { Data } = await client.CreateRecTask(params)
      const asrId = Data.TaskId
      return {
        code: 200,
        data: {
          asrId,
        },
      }
    } catch (err) {
      console.error(err)
      return {
        code: 400,
        message: 'asr fail',
      }
    }
  },
  async getAsr() {
	  const httpInfo = this.getHttpInfo()
	  let { taskId } = JSON.parse(httpInfo.body)
	  const client = new AsrClient(clientConfig);
	  const params = {
		  TaskId: taskId
	  };
	  
	  try {
	    const data = await client.DescribeTaskStatus(params)
	    
	    return {
	      code: 200,
	      data,
	    }
	  } catch (err) {
	    console.error(err)
	    return {
	      code: 400,
	      message: 'asr fail',
	    }
	  }
  },
}
