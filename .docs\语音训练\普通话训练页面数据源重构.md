# 普通话训练页面数据源重构需求

## 背景

当前 `src/pages/speak/putonghua-training.vue` 页面的数据获取依赖于接口请求，存在以下问题：
- 网络依赖性强，离线状态下无法使用
- 数据加载速度受网络环境影响
- 训练内容的一致性和可控性不足
- 无法灵活定制训练内容

为了提升用户体验，降低网络依赖，提高训练内容的质量和一致性，需要将数据源从接口请求重构为本地文件数据源。

## 需求

### 功能需求

#### 1. 数据源重构
- **本地数据文件管理**：在 `src/pages/speak/data/` 目录下为每个汉字创建独立的 `.js` 数据文件
- **数据文件结构标准化**：每个数据文件包含汉字的完整训练信息
- **动态数据加载**：页面启动时自动扫描并加载所有数据文件
- **动态分类生成**：通过读取所有数据文件，自动收集 `category` 字段值并去重后动态生成分类列表
- **分类展示**：根据动态生成的分类信息在"我的字库"区域按分类显示汉字

#### 2. 页面功能调整
- **字库展示优化**：保持现有UI样式，按动态生成的分类显示汉字
- **内容随机选择**：点击汉字时从对应数据文件中随机选择训练内容
- **交互逻辑保持**：维持现有的页面布局和用户交互方式
- **接口调用移除**：完全移除相关的API接口调用代码

#### 3. 数据文件规范
每个汉字的数据文件应包含：
- 汉字本身
- 分类信息（由数据文件动态决定）
- 相关词语及其拼音
- 每个词语关联的句子及其拼音

### 非功能需求

#### 1. 性能要求
- 页面初始加载时间不超过 2 秒
- 汉字切换响应时间不超过 500ms
- 支持懒加载，避免一次性加载过多数据

#### 2. 可维护性
- 数据文件结构清晰，易于扩展和维护
- 代码模块化，便于后续功能迭代
- 良好的错误处理机制

#### 3. 兼容性
- 使用全新的数据结构，不需要考虑与原有接口数据的兼容性
- 与录音练习功能无缝集成
- 支持现有的拼音显示/隐藏功能

## 技术方案

### 实现思路

#### 1. 数据文件结构设计
```javascript
// src/pages/speak/data/这.js
export default {
  character: '这',
  category: '翘舌音',
  words: [
    {
      text: '这个',
      pinyin: 'zhè ge',
      sentences: [
        { text: '这是一个美好的日子。', pinyin: 'zhè shì yí ge měi hǎo de rì zi。' },
        { text: '这个想法很不错。', pinyin: 'zhè ge xiǎng fǎ hěn bù cuò。' }
      ]
    },
    {
      text: '这里',
      pinyin: 'zhè lǐ',
      sentences: [
        { text: '这里的风景很美。', pinyin: 'zhè lǐ de fēng jǐng hěn měi。' },
        { text: '这里是我的家乡。', pinyin: 'zhè lǐ shì wǒ de jiā xiāng。' }
      ]
    },
    {
      text: '这样',
      pinyin: 'zhè yàng',
      sentences: [
        { text: '这样做是对的。', pinyin: 'zhè yàng zuò shì duì de。' },
        { text: '这样的天气很舒服。', pinyin: 'zhè yàng de tiān qì hěn shū fu。' }
      ]
    }
  ]
}
```

#### 2. 动态数据加载机制
使用 Webpack 的 `require.context` 或动态 `import` 实现：
```javascript
// 批量导入数据文件
const importAll = (r) => {
  const modules = {}
  r.keys().forEach((key) => {
    const moduleName = key.replace(/^\.\/(.*)\.js$/, '$1')
    modules[moduleName] = r(key).default
  })
  return modules
}

// 加载所有数据文件
const dataFiles = importAll(require.context('./data', false, /\.js$/))
```

#### 3. 动态分类生成和数据处理
```javascript
// 动态生成分类并按分类组织数据
const organizeByCategory = (dataFiles) => {
  const categories = {}

  // 自动收集所有分类并去重
  Object.values(dataFiles).forEach(data => {
    const category = data.category
    if (!categories[category]) {
      categories[category] = []
    }
    categories[category].push(data.character)
  })

  // 对每个分类内的汉字进行排序
  Object.keys(categories).forEach(category => {
    categories[category].sort((a, b) => a.localeCompare(b, 'zh-Hans-CN'))
  })

  return categories
}
```

### 架构设计

```mermaid
graph TD
    A[页面初始化] --> B[扫描data目录]
    B --> C[动态导入所有.js文件]
    C --> D[自动收集category字段]
    D --> E[去重生成分类列表]
    E --> F[构建字库分类结构]
    F --> G[渲染字库UI]

    H[用户点击汉字] --> I[获取对应数据文件]
    I --> J[随机选择词语]
    J --> K[随机选择该词语的句子]
    K --> L[更新页面显示]

    M[数据文件结构] --> N[汉字信息]
    M --> O[分类标签]
    M --> P[词语数组]
    P --> Q[每个词语的句子数组]
```

### 技术栈与约束

#### 技术栈
- **前端框架**: Vue.js 3 + Composition API
- **构建工具**: Webpack (uni-app内置)
- **数据格式**: ES6 模块 (.js文件)
- **动态导入**: require.context 或 dynamic import

#### 约束条件
- 数据文件大小单个不超过 50KB
- 总数据文件数量控制在 200 个以内
- 兼容 uni-app 的打包机制
- 支持 H5、小程序、App 多端运行

#### 实现步骤
1. **数据文件创建**：基于新的简化数据结构创建标准模板
2. **动态分类实现**：实现自动收集category字段的分类生成机制
3. **加载机制实现**：实现动态批量导入功能
4. **字库显示重构**：修改字库显示逻辑以支持动态分类
5. **内容选择优化**：实现词语和句子的随机选择逻辑
6. **接口调用清理**：移除所有相关API调用
7. **测试验证**：确保功能完整性和性能表现

## 风险评估

### 假设与未知因素

- **假设**：uni-app 的 require.context 在所有目标平台上都能正常工作
- **假设**：本地数据文件的加载性能优于网络请求
- **未知因素**：不同平台对动态导入的支持程度差异
- **未知因素**：大量数据文件对应用包体积的影响

### 潜在风险

#### 1. 技术风险
- **动态导入兼容性问题**：某些平台可能不支持 require.context
- **打包体积增加**：本地数据文件会增加应用包体积
- **内存占用**：一次性加载所有数据可能影响内存使用

**解决方案**：
- 实现降级方案，支持静态导入
- 优化数据文件大小，实现按需加载
- 实现数据缓存和内存管理机制

#### 2. 数据管理风险
- **数据结构一致性**：多个数据文件的格式可能不统一
- **分类命名规范**：不同数据文件中的category字段可能存在命名不一致
- **维护复杂性**：数据文件数量增加后维护成本上升

**解决方案**：
- 建立数据文件结构校验机制
- 制定分类命名规范和标准词典
- 提供数据文件生成和验证工具
- 制定详细的数据文件规范文档

#### 3. 用户体验风险
- **首次加载延迟**：批量导入可能导致初始化时间增加
- **功能回退**：重构过程中可能暂时影响现有功能

**解决方案**：
- 实现渐进式加载和加载状态提示
- 采用分阶段重构，确保功能连续性
- 充分的测试验证和回滚预案
