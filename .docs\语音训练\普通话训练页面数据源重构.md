# 普通话训练页面数据源重构需求

## 背景

当前 `src/pages/speak/putonghua-training.vue` 页面的数据获取依赖于接口请求，存在以下问题：
- 网络依赖性强，离线状态下无法使用
- 数据加载速度受网络环境影响
- 训练内容的一致性和可控性不足
- 无法灵活定制训练内容

为了提升用户体验，降低网络依赖，提高训练内容的质量和一致性，需要将数据源从接口请求重构为本地文件数据源。

## 需求

### 功能需求

#### 1. 数据源重构
- **本地数据文件管理**：在 `src/pages/speak/data/` 目录下为每个汉字创建独立的 `.js` 数据文件
- **数据文件结构标准化**：每个数据文件包含汉字的完整训练信息
- **动态数据加载**：页面启动时自动扫描并加载所有数据文件
- **分类展示**：根据数据文件中的分类信息在"我的字库"区域按分类显示汉字

#### 2. 页面功能调整
- **字库展示优化**：保持现有UI样式，按分类显示汉字（如：平舌音、翘舌音、前鼻音、后鼻音等）
- **内容随机选择**：点击汉字时从对应数据文件中随机选择训练内容
- **交互逻辑保持**：维持现有的页面布局和用户交互方式
- **接口调用移除**：完全移除相关的API接口调用代码

#### 3. 数据文件规范
每个汉字的数据文件应包含：
- 汉字本身
- 分类信息（平舌音、翘舌音、前鼻音、后鼻音等）
- 相关词语及其拼音
- 相关成语及其拼音
- 相关句子及其拼音
- 训练相关的元数据（难度等级、训练重点等）

### 非功能需求

#### 1. 性能要求
- 页面初始加载时间不超过 2 秒
- 汉字切换响应时间不超过 500ms
- 支持懒加载，避免一次性加载过多数据

#### 2. 可维护性
- 数据文件结构清晰，易于扩展和维护
- 代码模块化，便于后续功能迭代
- 良好的错误处理机制

#### 3. 兼容性
- 与现有训练记录功能完全兼容
- 与录音练习功能无缝集成
- 支持现有的拼音显示/隐藏功能

## 技术方案

### 实现思路

#### 1. 数据文件结构设计
```javascript
// src/pages/speak/data/这.js
export default {
  character: '这',
  category: '翘舌音',
  difficulty: 'medium',
  keywords: ['翘舌音', 'zh音'],
  wordsAndPhrases: [
    { text: '这个', pinyin: 'zhè ge' },
    { text: '这里', pinyin: 'zhè lǐ' },
    { text: '这样', pinyin: 'zhè yàng' }
  ],
  sentences: [
    { text: '这是一个美好的日子。', pinyin: 'zhè shì yí ge měi hǎo de rì zi。' },
    { text: '这里的风景很美。', pinyin: 'zhè lǐ de fēng jǐng hěn měi。' }
  ],
  idioms: [
    { text: '这山望着那山高', pinyin: 'zhè shān wàng zhe nà shān gāo' }
  ],
  metadata: {
    trainingFocus: '翘舌音zh的发音',
    commonMistakes: ['容易发成平舌音z'],
    tips: '舌尖向上卷起，与硬腭接触'
  }
}
```

#### 2. 动态数据加载机制
使用 Webpack 的 `require.context` 或动态 `import` 实现：
```javascript
// 批量导入数据文件
const importAll = (r) => {
  const modules = {}
  r.keys().forEach((key) => {
    const moduleName = key.replace(/^\.\/(.*)\.js$/, '$1')
    modules[moduleName] = r(key).default
  })
  return modules
}

// 加载所有数据文件
const dataFiles = importAll(require.context('./data', false, /\.js$/))
```

#### 3. 分类数据处理
```javascript
// 按分类组织数据
const organizeByCategory = (dataFiles) => {
  const categories = {}
  Object.values(dataFiles).forEach(data => {
    const category = data.category
    if (!categories[category]) {
      categories[category] = []
    }
    categories[category].push(data.character)
  })
  return categories
}
```

### 架构设计

```mermaid
graph TD
    A[页面初始化] --> B[扫描data目录]
    B --> C[动态导入所有.js文件]
    C --> D[解析分类信息]
    D --> E[构建字库分类结构]
    E --> F[渲染字库UI]
    
    G[用户点击汉字] --> H[获取对应数据文件]
    H --> I[随机选择训练内容]
    I --> J[更新页面显示]
    
    K[数据文件] --> L[汉字信息]
    K --> M[分类标签]
    K --> N[词语/成语]
    K --> O[句子内容]
    K --> P[元数据]
```

### 技术栈与约束

#### 技术栈
- **前端框架**: Vue.js 3 + Composition API
- **构建工具**: Webpack (uni-app内置)
- **数据格式**: ES6 模块 (.js文件)
- **动态导入**: require.context 或 dynamic import

#### 约束条件
- 数据文件大小单个不超过 50KB
- 总数据文件数量控制在 200 个以内
- 兼容 uni-app 的打包机制
- 支持 H5、小程序、App 多端运行

#### 实现步骤
1. **数据文件创建**：基于示例文件创建标准数据结构
2. **加载机制实现**：实现动态批量导入功能
3. **分类逻辑重构**：修改字库显示逻辑
4. **内容选择优化**：实现随机内容选择
5. **接口调用清理**：移除所有相关API调用
6. **测试验证**：确保功能完整性和性能表现

## 风险评估

### 假设与未知因素

- **假设**：uni-app 的 require.context 在所有目标平台上都能正常工作
- **假设**：本地数据文件的加载性能优于网络请求
- **未知因素**：不同平台对动态导入的支持程度差异
- **未知因素**：大量数据文件对应用包体积的影响

### 潜在风险

#### 1. 技术风险
- **动态导入兼容性问题**：某些平台可能不支持 require.context
- **打包体积增加**：本地数据文件会增加应用包体积
- **内存占用**：一次性加载所有数据可能影响内存使用

**解决方案**：
- 实现降级方案，支持静态导入
- 优化数据文件大小，实现按需加载
- 实现数据缓存和内存管理机制

#### 2. 数据管理风险
- **数据一致性**：多个数据文件的格式可能不统一
- **维护复杂性**：数据文件数量增加后维护成本上升

**解决方案**：
- 建立数据文件校验机制
- 提供数据文件生成工具
- 制定详细的数据文件规范文档

#### 3. 用户体验风险
- **首次加载延迟**：批量导入可能导致初始化时间增加
- **功能回退**：重构过程中可能暂时影响现有功能

**解决方案**：
- 实现渐进式加载和加载状态提示
- 采用分阶段重构，确保功能连续性
- 充分的测试验证和回滚预案
